@model Odrdc.Dots.Areas.Transfers.Models.Mosci.MosciPageViewModel

@{
    ViewBag.Title = "MOSCI";
}




@if (!string.IsNullOrWhiteSpace(Model.Message))
{
    <div class="alert alert-success fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @Model.Message
    </div>
}
@if (!string.IsNullOrWhiteSpace(Model.ErrorMessage))
{
    <div class="alert alert-danger fade in">
        <a href="#" class="close" data-dismiss="alert" aria-label="close">&times;</a>
        @Model.ErrorMessage
    </div>
}






@using (Html.BeginForm("Index", "Mosci", new { area = "Transfers" }, FormMethod.Post, true, new { @id = "Mosci", @class = "form-horizontal" }))
{
    @Html.AntiForgeryToken()



    @* Hidden fields for auto-population functionality *@
    @Html.Hidden("autoPopulateRowIndex", "", new { id = "autoPopulateRowIndex" })

    <div id="Housing-Manage" class="no-print">
        <div class="row">
            <div class="col-md-12">
                <div class="divFindOffender">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            Find Inmate
                        </div>
                        <div class="panel-body">
                            <div class="form-inline">
                                <div class="form-group col-xs-12 col-sm-6 col-md-6">
                                    @Html.DropDownListFor(m => m.SearchPrefix, Model.PrefixOptions, new { @class = "form-control input-sm", @id = "searchPrefixDropdown" })
                                    @Html.TextBoxFor(m => m.SearchOffenderId, new { @class = "form-control input-sm onlyNumeric", @autofocus = "autofocus", @id = "txtInmateNum", maxlength = "6" })
                                    <button type="submit" class="btn btn-primary" name="submitAction" value="Search" id="btnFindInmateServer">
                                        <span>Find Inmate</span>
                                    </button>
                                    <button id="btnFindOffender" type="button" class="btn btn-info" title="AJAX search">
                                        <span class="glyphicon glyphicon-search"></span> AJAX Search
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    //-------------------------------------------------
    <div class="panel panel-primary">
        <div class="panel-heading">
            Schedule Inmate Move&nbsp;&nbsp;-&nbsp;&nbsp;MOSCI
        </div>
        <div class="panel-body">
            <div class="table-responsive">
                <table id="inmateTable" class="table table-bordered table-condensed">
                    <thead class="odrc-header-row">
                        <tr>
                            <td style="width:60px;">Prefix</td>
                            <td style="width:120px;">Offender #</td>
                            <td style="width:150px;">Last Name</td>
                            <td style="width:150px;">First Name</td>
                            <td style="width:120px;">From</td>
                            <td style="width:140px;">To</td>
                            <td style="width:140px;">Scheduled Date</td>
                            <td style="width:80px;">Comments</td>
                            <td style="width:80px;">Remove</td>
                            <td style="width:80px;">Delete</td>
                        </tr>
                    </thead>
                    <tbody>
                        @for (int i = 0; i < Model.Inmates.Count; i++)
                        {
                            <tr @if (i == 0) { <text>id="inmate-row-template"</text> }>
                                <td>
                                    @Html.DropDownListFor(m => m.Inmates[i].InmateIdPrefix, Model.PrefixOptions, new { @class = "form-control input-sm" })
                                    @Html.HiddenFor(m => m.Inmates[i].Recno)
                                    @Html.HiddenFor(m => m.Inmates[i].OffenderId)
                                </td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].CombinedOffenderId, new { @class = "form-control input-sm auto-populate-field", @maxlength = "7", @data_row_index = i })</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].LastName, new { @class = "form-control input-sm", @readonly = "readonly"})</td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].FirstName, new { @class = "form-control input-sm", @readonly = "readonly" })</td>
                                <td>
                                    @{
                                        var fromInstitutionText = "";
                                        if (Model.Inmates[i].FromInstitutionId.HasValue)
                                        {
                                            var fromOption = Model.FromInstitutionOptions.FirstOrDefault(x => x.Value == Model.Inmates[i].FromInstitutionId.ToString());
                                            fromInstitutionText = fromOption?.Text ?? "";
                                        }
                                    }
                                    @Html.TextBoxFor(m => m.Inmates[i].FromInstitutionId, new { @class = "form-control input-sm", @readonly = "readonly", @style = "display:none;" })
                                    @Html.TextBox($"Inmates[{i}].FromInstitutionDisplay", fromInstitutionText, new { @class = "form-control input-sm", @readonly = "readonly" })
                                </td>
                                <td>@Html.DropDownListFor(m => m.Inmates[i].ToInstitutionId, Model.ToInstitutionOptions, new { @class = "form-control input-sm" })</td>
                                <td>
                                    <div class="input-group input-group-sm">
                                        @Html.TextBoxFor(m => m.Inmates[i].SchDate, "{0:MM/dd/yyyy}", new { @class = "form-control input-sm datepicker-input" })
                                        <span class="input-group-addon datepicker-trigger" style="cursor: pointer;">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </td>
                                <td>@Html.TextBoxFor(m => m.Inmates[i].Descrl, new { @class = "form-control input-sm" })</td>
                                <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForRemoval)</td>
                                <td class="text-center">@Html.CheckBoxFor(m => m.Inmates[i].IsMarkedForDeletion)</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>

            <!-- Add / Remove Row Buttons -->
            <div class="row">
                <div class="col-md-6 col-xs-12">
                    <!-- Commented out server-side functionality, now using JavaScript -->
                    <!-- <button type="submit" name="submitAction" value="AddNew" id="btnAddNewInmate" class="btn btn-primary"> -->
                    <button type="button" id="btnAddNewInmate" class="btn btn-primary">
                        <span class="glyphicon glyphicon-plus"></span> Add New Inmate
                    </button>
                </div>
                <div class="col-md-6 col-xs-12 text-right">
                    <!-- Commented out server-side functionality, now using JavaScript -->
                    <!-- <button type="submit" name="submitAction" value="RemoveSelected" name="btnRemoveInmate" class="btn btn-danger"> -->
                    <button type="button" id="btnRemoveInmate" class="btn btn-danger">
                        <span class="glyphicon glyphicon-remove"></span> Remove Inmate
                    </button>
                    <span style="display:inline-block; width:20px;"></span>
                    <button type="submit" name="submitAction" value="DeleteSelected" name="btnDelete" class="btn btn-default">
                        <span class="glyphicon glyphicon-trash"></span> Delete
                    </button>
                </div>
            </div>
            <br />
            <!-- Save / Cancel Buttons -->
            <div class="row text-center">
                <button type="submit" name="submitAction" value="Save" class="btn btn-primary" id="btnSave">
                    <span class="glyphicon glyphicon-floppy-disk"></span> Save
                </button>
                <button type="submit" name="submitAction" value="Cancel" class="btn btn-default" id="btnCancel">
                    <span class="glyphicon glyphicon-remove-circle"></span> Cancel
                </button>
            </div>
        </div>
    </div>



    @section Scripts {
        <!-- Use jQuery UI from CDN -->
        <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css"
              integrity="sha256-fECPsHzFOcZLdG/y4+W8y0fyKn/KYAJdtLyV8UaWb0g="
              crossorigin="anonymous">
        <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"
                integrity="sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0="
                crossorigin="anonymous"></script>


        <script type="text/javascript">
            var MOSCI = MOSCI || {};
            MOSCI.MAX_ROWS = 19;

            // Auto-populate function for offender data
            function autoPopulateOffender(inputElement, rowIndex) {
                var combinedOffenderId = inputElement.value.trim();
                console.log('=== Auto-populate triggered ===');
                console.log('Row Index:', rowIndex);
                console.log('Combined Offender ID:', combinedOffenderId);
                console.log('Total rows in table:', $('#inmateTable tbody tr').length);

                if (!combinedOffenderId) {
                    // Clear fields if offender ID is empty
                    clearOffenderFields(rowIndex);
                    return;
                }

                // Extract prefix and offender ID from combined input for client-side validation
                var prefix = "";
                var offenderId = combinedOffenderId;

                // If the combined ID starts with a letter (prefix), extract it
                if (combinedOffenderId.length > 0 && /^[A-Za-z]/.test(combinedOffenderId[0])) {
                    prefix = combinedOffenderId[0].toUpperCase();
                    offenderId = combinedOffenderId.substring(1);
                }

                console.log('Extracted prefix:', prefix);
                console.log('Extracted offender ID:', offenderId);

                // Update the prefix dropdown and hidden OffenderId field before submission
                var $row = $('#inmateTable tbody tr').eq(rowIndex);
                if (prefix) {
                    $row.find('select[id*="InmateIdPrefix"]').val(prefix);
                }
                $row.find('input[id*="OffenderId"]').val(offenderId);

                console.log('Updated prefix dropdown to:', prefix);
                console.log('Updated hidden OffenderId to:', offenderId);

                // Ensure all rows have proper field names before submission
                console.log('Ensuring proper field names before submission...');
                MOSCI.updateRowIndices();

                // Debug: Log all field names that will be submitted
                console.log('=== Field names before submission ===');
                $('#inmateTable tbody tr').each(function(index) {
                    var $row = $(this);
                    console.log(`Row ${index}:`);
                    $row.find('input, select').each(function() {
                        var $field = $(this);
                        var name = $field.attr('name');
                        var value = $field.val();
                        if (name && name.includes('Inmates[')) {
                            console.log(`  ${name} = "${value}"`);
                        }
                    });
                });

                // Set hidden fields and submit form for server-side processing
                document.getElementById('autoPopulateRowIndex').value = rowIndex;

                // Create a temporary hidden input for submitAction
                var submitActionInput = document.createElement('input');
                submitActionInput.type = 'hidden';
                submitActionInput.name = 'submitAction';
                submitActionInput.value = 'AutoPopulate';
                document.getElementById('Mosci').appendChild(submitActionInput);

                console.log('Submitting form for auto-population...');
                document.getElementById('Mosci').submit();
            }

            // Event delegation for auto-populate functionality
            $(document).on('change', '.auto-populate-field', function() {
                var rowIndex = $(this).data('row-index') || $(this).closest('tr').index();
                autoPopulateOffender(this, rowIndex);
            });

            $(document).on('keydown', '.auto-populate-field', function(event) {
                if (event.keyCode == 13 || event.keyCode == 9) { // Enter or Tab
                    var rowIndex = $(this).data('row-index') || $(this).closest('tr').index();
                    autoPopulateOffender(this, rowIndex);
                    return false;
                }
            });

            // Function to clear offender fields
            function clearOffenderFields(rowIndex) {
                var $row = $('#inmateTable tbody tr').eq(rowIndex);
                $row.find('input[id*="LastName"]').val('');
                $row.find('input[id*="FirstName"]').val('');
                $row.find('input[name*="FromInstitutionDisplay"]').val('');
                $row.find('input[id*="FromInstitutionId"]').val('');
                $row.find('select[id*="InmateIdPrefix"]').val('');
                $row.find('input[id*="OffenderId"]').val('');
            }

            // Function to update From Institution display text based on ID
            function updateFromInstitutionDisplay(rowIndex, institutionId) {
                var $row = $('#inmateTable tbody tr').eq(rowIndex);
                var fromInstitutionOptions = @Html.Raw(Json.Serialize(Model.FromInstitutionOptions));
                var displayText = '';

                if (institutionId) {
                    var option = fromInstitutionOptions.find(function(opt) {
                        return opt.Value == institutionId.toString();
                    });
                    displayText = option ? option.Text : '';
                }

                $row.find('input[name*="FromInstitutionDisplay"]').val(displayText);
                $row.find('input[id*="FromInstitutionId"]').val(institutionId || '');
            }

            // Function to add a new inmate row
            MOSCI.addNewInmate = function() {
                var currentRowCount = $('#inmateTable tbody tr').length;
                if (currentRowCount >= MOSCI.MAX_ROWS) {
                    console.log('Maximum number of rows reached');
                    return null;
                }

                // Clone the template row
                var $newRow = $('#inmate-row-template').clone();

                // Generate a unique ID for the new row
                var rowId = 'inmate-row-' + new Date().getTime();
                $newRow.attr('id', rowId);

                // Update the name attributes for proper MVC model binding
                var newIndex = currentRowCount;
                $newRow.find('select, input').each(function() {
                    var $element = $(this);
                    var name = $element.attr('name');
                    var id = $element.attr('id');

                    if (name && name.includes('[0]')) {
                        $element.attr('name', name.replace('[0]', '[' + newIndex + ']'));
                    }
                    if (id && id.includes('_0_')) {
                        $element.attr('id', id.replace('_0_', '_' + newIndex + '_'));
                    }

                    // Update data-row-index for auto-populate fields
                    if ($element.hasClass('auto-populate-field')) {
                        $element.attr('data-row-index', newIndex);
                    }
                });

                // Clear all input values
                $newRow.find('input[type="text"]').val('');
                $newRow.find('select').prop('selectedIndex', 0);
                $newRow.find('input[type="checkbox"]').prop('checked', false);
                $newRow.find('input[type="hidden"]').val('');

                // Append the new row to the table
                $('#inmateTable tbody').append($newRow);

                // Apply numeric validation to the new row (exclude CombinedOffenderId fields)
                $newRow.find('.onlyNumeric').not('[id*="CombinedOffenderId"]').on('keypress', function(e) {
                    // Allow only numbers (0-9)
                    if (e.which < 48 || e.which > 57) {
                        e.preventDefault();
                    }
                });

                // Initialize datepicker for the new row
                $newRow.find('.datepicker-input').each(function() {
                    var $input = $(this);
                    if (!$input.hasClass('hasDatepicker')) {
                        $input.datepicker({
                            dateFormat: 'mm/dd/yy',
                            changeMonth: true,
                            changeYear: true,
                            showButtonPanel: false,
                            yearRange: '-10:+10',
                            showOtherMonths: true,
                            selectOtherMonths: false,
                            firstDay: 0 // Sunday
                        });
                    }
                });

                // Update row indices after adding the new row
                MOSCI.updateRowIndices();

                return $newRow;
            };

            // Function to clear the table except for one empty row
            MOSCI.clearTable = function() {
                $('#inmateTable tbody tr').not(':first').remove();
                var $firstRow = $('#inmateTable tbody tr:first');
                $firstRow.find('input[type="text"]').val('');
                $firstRow.find('select').prop('selectedIndex', 0);
                $firstRow.find('input[type="checkbox"]').prop('checked', false);
                $firstRow.find('input[type="hidden"]').val('');
            };

            // Function to update row indices and field names after adding/removing rows
            MOSCI.updateRowIndices = function() {
                $('#inmateTable tbody tr').each(function(index) {
                    var $row = $(this);

                    // Update all input field names and IDs to use the correct index
                    $row.find('input, select').each(function() {
                        var $field = $(this);
                        var name = $field.attr('name');
                        var id = $field.attr('id');

                        if (name && name.includes('Inmates[')) {
                            // Update the index in the name attribute
                            var newName = name.replace(/Inmates\[\d+\]/, 'Inmates[' + index + ']');
                            $field.attr('name', newName);
                        }

                        if (id && id.includes('Inmates_')) {
                            // Update the index in the id attribute
                            var newId = id.replace(/Inmates_\d+_/, 'Inmates_' + index + '_');
                            $field.attr('id', newId);
                        }
                    });

                    // Update data-row-index attribute for auto-populate fields
                    $row.find('.auto-populate-field').attr('data-row-index', index);
                });

                console.log('Updated row indices for', $('#inmateTable tbody tr').length, 'rows');
            };

            // Function to initialize datepickers
            MOSCI.initializeDatepickers = function() {
                // Initialize datepickers for all date inputs that don't already have it
                $('.datepicker-input').each(function() {
                    var $input = $(this);
                    if (!$input.hasClass('hasDatepicker')) {
                        console.log('Initializing datepicker for input:', $input[0]);
                        try {
                            $input.datepicker({
                                dateFormat: 'mm/dd/yy',
                                changeMonth: true,
                                changeYear: true,
                                showButtonPanel: false,
                                yearRange: '-10:+10',
                                showOtherMonths: true,
                                selectOtherMonths: false,
                                firstDay: 0, // Sunday
                                onSelect: function(dateText) {
                                    console.log('Date selected via datepicker:', dateText);
                                    $(this).trigger('change');
                                }
                            });

                            // Also add click handler to input itself
                            $input.on('focus click', function() {
                                console.log('Input focused/clicked, showing datepicker');
                                $(this).datepicker('show');
                            });

                            console.log('Datepicker initialized for input');
                        } catch (error) {
                            console.error('Error initializing datepicker:', error);
                        }
                    }
                });
            };

            // Handle calendar icon clicks with event delegation - simplified approach
            $(document).on('click', '.datepicker-trigger', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var $trigger = $(this);
                var $input = $trigger.siblings('.datepicker-input');

                console.log('=== Calendar Icon Clicked ===');
                console.log('Trigger element:', $trigger[0]);
                console.log('Input found:', $input.length);
                console.log('Input element:', $input[0]);

                if ($input.length > 0) {
                    console.log('Input has datepicker class:', $input.hasClass('hasDatepicker'));

                    // Force initialize datepicker if not already done
                    if (!$input.hasClass('hasDatepicker')) {
                        console.log('Force initializing datepicker...');
                        try {
                            $input.datepicker({
                                dateFormat: 'mm/dd/yy',
                                changeMonth: true,
                                changeYear: true,
                                showButtonPanel: false,
                                yearRange: '-10:+10',
                                showOtherMonths: true,
                                selectOtherMonths: false,
                                firstDay: 0,
                                onSelect: function(dateText) {
                                    console.log('Date selected:', dateText);
                                    $(this).trigger('change');
                                }
                            });
                            console.log('Datepicker initialized successfully');
                        } catch (initError) {
                            console.error('Error initializing datepicker:', initError);
                            return;
                        }
                    }

                    // Try to show the datepicker
                    try {
                        console.log('Attempting to show datepicker...');
                        $input.datepicker('show');
                        console.log('Datepicker show command executed');
                    } catch (showError) {
                        console.error('Error showing datepicker:', showError);
                        // Alternative approach: focus the input which should trigger the datepicker
                        console.log('Trying alternative approach - focusing input');
                        $input.focus();

                        // If that doesn't work, try triggering a click event
                        setTimeout(function() {
                            $input.trigger('click');
                        }, 50);
                    }
                } else {
                    console.error('No input sibling found for calendar trigger');
                    console.log('Trigger parent:', $trigger.parent()[0]);
                    console.log('All inputs in parent:', $trigger.parent().find('input').length);
                }
            });

        $(function () {
            // Debug: Log dropdown options on page load
            console.log('ToInstitutionOptions available:', @Html.Raw(Json.Serialize(Model.ToInstitutionOptions)));
            console.log('FromInstitutionOptions available:', @Html.Raw(Json.Serialize(Model.FromInstitutionOptions)));

            // Check if jQuery UI is loaded
            console.log('jQuery UI loaded:', typeof $.fn.datepicker !== 'undefined');
            console.log('jQuery version:', $.fn.jquery);

            // Wait a bit for jQuery UI to be fully loaded
            setTimeout(function() {
                console.log('=== Initializing Datepickers ===');
                console.log('Found datepicker inputs:', $('.datepicker-input').length);

                // Initialize datepickers for existing rows
                MOSCI.initializeDatepickers();

                // Verify initialization
                setTimeout(function() {
                    console.log('Datepicker inputs with hasDatepicker class:', $('.datepicker-input.hasDatepicker').length);

                    // Test direct click on first input
                    var $firstInput = $('.datepicker-input').first();
                    if ($firstInput.length > 0) {
                        console.log('First input element:', $firstInput[0]);
                        console.log('First input has datepicker:', $firstInput.hasClass('hasDatepicker'));
                    }
                }, 200);
            }, 100);

            // Update From Institution display fields on page load (for auto-populated data)
            $('#inmateTable tbody tr').each(function(index) {
                var $row = $(this);
                var fromInstitutionId = $row.find('input[id*="FromInstitutionId"]').val();
                if (fromInstitutionId) {
                    updateFromInstitutionDisplay(index, fromInstitutionId);
                }
            });

            // Debug form submission
            $('#Mosci').on('submit', function(e) {
                console.log('Form submission detected');
                var formData = $(this).serialize();
                console.log('Form data:', formData);

                // Check if this is a search submission - look for the clicked button
                var submitAction = $(document.activeElement).val() || $('button[type="submit"]:focus').val();
                console.log('Submit action:', submitAction);

                // Log search-specific values
                var searchPrefix = $('#searchPrefixDropdown').val();
                var searchOffenderId = $('#txtInmateNum').val();
                console.log('Search values - Prefix:', searchPrefix, 'OffenderId:', searchOffenderId);

                if (submitAction === 'Search') {
                    console.log('Search form submission detected');
                    console.log('Final search values being submitted - Prefix:', searchPrefix, 'OffenderId:', searchOffenderId);
                }
            });



            // JavaScript-only functionality for Add New Inmate and Remove Inmate buttons

            // Add New Inmate button - client-side only
            $('#btnAddNewInmate').on('click', function(e) {
                e.preventDefault();
                console.log('Add New Inmate button clicked (JavaScript)');

                var currentRowCount = $('#inmateTable tbody tr').length;
                if (currentRowCount >= MOSCI.MAX_ROWS) {
                    alert('Maximum number of rows (' + MOSCI.MAX_ROWS + ') reached. Cannot add more rows.');
                    return;
                }

                var $newRow = MOSCI.addNewInmate();
                if ($newRow) {
                    console.log('New inmate row added successfully');
                    // Focus on the first input field of the new row
                    $newRow.find('input[id*="CombinedOffenderId"]').focus();
                } else {
                    alert('Failed to add new row. Maximum number of rows may have been reached.');
                }
            });

            // Remove Inmate button - client-side only
            $('#btnRemoveInmate').on('click', function(e) {
                e.preventDefault();
                console.log('Remove Inmate button clicked (JavaScript)');

                // Find all checked remove checkboxes
                var $checkedRows = $('#inmateTable tbody tr').filter(function() {
                    return $(this).find('input[id*="IsMarkedForRemoval"]:checked').length > 0;
                });

                if ($checkedRows.length === 0) {
                    alert('Please select at least one inmate to remove by checking the Remove checkbox.');
                    return;
                }

                // Ensure at least one row remains
                var totalRows = $('#inmateTable tbody tr').length;
                if (totalRows - $checkedRows.length < 1) {
                    alert('Cannot remove all rows. At least one row must remain in the table.');
                    return;
                }

                // Confirm removal
                var confirmMessage = 'Are you sure you want to remove ' + $checkedRows.length + ' selected inmate(s)?';
                if (confirm(confirmMessage)) {
                    $checkedRows.remove();
                    console.log('Removed ' + $checkedRows.length + ' inmate row(s)');

                    // Update row indices and field names after removal
                    MOSCI.updateRowIndices();
                }
            });

            // Handle cancel button
            $('#btnCancel').on('click', function() {
                if (confirm('Are you sure you want to cancel? Any unsaved changes will be lost.')) {
                    window.location.href = '@Url.Action("Index", "Home", new { area = "" })';
                }
            });




            // Initialize numeric validation for existing rows (exclude CombinedOffenderId fields)
            $('.onlyNumeric').not('[id*="CombinedOffenderId"]').on('keypress', function(e) {
                // Allow only numbers (0-9), backspace, delete, tab, escape, enter
                if ($.inArray(e.keyCode, [46, 8, 9, 27, 13, 110]) !== -1 ||
                    // Allow Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
                    (e.keyCode === 65 && e.ctrlKey === true) ||
                    (e.keyCode === 67 && e.ctrlKey === true) ||
                    (e.keyCode === 86 && e.ctrlKey === true) ||
                    (e.keyCode === 88 && e.ctrlKey === true) ||
                    // Allow home, end, left, right
                    (e.keyCode >= 35 && e.keyCode <= 39)) {
                    return;
                }
                // Ensure that it is a number and stop the keypress
                if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                    e.preventDefault();
                }
            });

            // Add event handler for prefix dropdown changes to update combined offender ID
            $(document).on('change', 'select[id*="InmateIdPrefix"]', function() {
                var $row = $(this).closest('tr');
                var prefix = $(this).val();
                var offenderId = $row.find('input[id*="OffenderId"]').val();
                var combinedId = prefix && offenderId ? prefix + offenderId : '';
                $row.find('input[id*="CombinedOffenderId"]').val(combinedId);
                console.log('Prefix changed - Updated CombinedOffenderId to:', combinedId);
            });

            // Add event handler for hidden OffenderId changes to update combined offender ID
            $(document).on('change', 'input[id*="OffenderId"]', function() {
                var $row = $(this).closest('tr');
                var prefix = $row.find('select[id*="InmateIdPrefix"]').val();
                var offenderId = $(this).val();
                var combinedId = prefix && offenderId ? prefix + offenderId : '';
                $row.find('input[id*="CombinedOffenderId"]').val(combinedId);
                console.log('OffenderId changed - Updated CombinedOffenderId to:', combinedId);
            });




            // Handle server-side Find Inmate button
            $('#btnFindInmateServer').on('click', function(e) {
                console.log('Find Inmate (Server) button clicked');

                var prefix = $('#searchPrefixDropdown').val();
                var offenderId = $('#txtInmateNum').val();

                console.log('Form values - Prefix:', prefix, 'OffenderId:', offenderId);

                // Validate offenderId
                if (!offenderId || offenderId.trim() === '') {
                    alert('Please enter an Offender ID.');
                    e.preventDefault();
                    return false;
                }

                console.log('Validation passed, submitting form...');
                console.log('Button value:', $(this).val());
                console.log('Button name:', $(this).attr('name'));

                // The form fields are already bound to the model properties
                // The button's name="submitAction" and value="Search" should be submitted automatically
                return true;
            });

            // Handle AJAX find offender button
            $('#btnFindOffender').click(function (e) {
                e.preventDefault();
                var prefix = $('#searchPrefixDropdown').val();
                var offenderId = $('#txtInmateNum').val();

                // Validate offenderId
                if (!offenderId || offenderId.trim() === '') {
                    alert('Please enter an Offender ID.');
                    return;
                }

                // Trim any whitespace
                offenderId = offenderId.trim();

                // Concatenate the prefix with the offender ID for searching
                var searchOffenderId = prefix + offenderId;

                // Log the values for debugging
                console.log('Searching for inmate with prefix:', prefix, 'and offenderId:', offenderId);
                console.log('Combined search ID:', searchOffenderId);

                $(this).prop('disabled', true)
                       .html('<span class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></span> Searching…');

                MOSCI.clearTable();

                $.ajax({
                    url: '@Url.Action("FindInmate", "Mosci", new { area = "Transfers" })',
                    type: 'POST',
                    data: { searchOffenderId: searchOffenderId },
                    success: function (result) {
                        $('#btnFindOffender').prop('disabled', false).html('<span>Find Inmate</span>');
                        console.log('Server response:', result);

                        if (result.success) {
                            var inmates = result.inmates;
                            var $first = $('#inmateTable tbody tr').first();
                            var isEmpty = !$first.find('input[id*="CombinedOffenderId"]').val();

                            inmates.forEach(function (inmate, i) {
                                var $row;

                                if (i === 0 && isEmpty) {
                                    $row = $first;
                                } else {
                                    // Use the MOSCI.addNewInmate function to add a new row
                                    $row = MOSCI.addNewInmate();
                                }

                                if (!$row || $row.length === 0) {
                                    console.error('Failed to get row for inmate #' + (i + 1));
                                    return;
                                }

                                                // Set the prefix dropdown
                                $row.find('select[id*="InmateIdPrefix"]').val(inmate.inmateIdPrefix);

                                // Set the hidden offender ID field
                                $row.find('input[id*="OffenderId"]').val(inmate.offenderId);

                                // Set the combined offender ID display field
                                $row.find('input[id*="CombinedOffenderId"]').val(inmate.inmateIdPrefix + inmate.offenderId);

                                // Set other fields
                                $row.find('input[id*="LastName"]').val(inmate.lastName);
                                $row.find('input[id*="FirstName"]').val(inmate.firstName);

                                // Set the institution dropdowns based on the institution IDs
                                console.log('Setting FromInstitutionId:', inmate.fromInstitutionId);
                                console.log('Setting ToInstitutionId:', inmate.toInstitutionId);

                                // Set From Institution dropdown
                                if (inmate.fromInstitutionId) {
                                    var $fromSelect = $row.find('select[id*="FromInstitutionId"]');
                                    console.log('From dropdown found:', $fromSelect.length);
                                    if ($fromSelect.length > 0) {
                                        $fromSelect.val(inmate.fromInstitutionId);
                                        console.log('From dropdown value set to:', $fromSelect.val());
                                        // Trigger change event to ensure proper binding
                                        $fromSelect.trigger('change');
                                    }
                                }

                                // Set To Institution dropdown with enhanced selection logic
                                if (inmate.toInstitutionId) {
                                    var $toSelect = $row.find('select[id*="ToInstitutionId"]');
                                    console.log('To dropdown found:', $toSelect.length);
                                    if ($toSelect.length > 0) {
                                        // Debug: Log available options
                                        console.log('Available options in To dropdown:');
                                        $toSelect.find('option').each(function() {
                                            console.log('  Option value:', $(this).val(), 'text:', $(this).text());
                                        });
                                        // First, try setting the value directly
                                        $toSelect.val(inmate.toInstitutionId);
                                        console.log('To dropdown value after .val():', $toSelect.val());

                                        // If direct value setting didn't work, try selecting by option value
                                        if ($toSelect.val() !== inmate.toInstitutionId.toString()) {
                                            console.log('Direct value setting failed, trying option selection');
                                            $toSelect.find('option').prop('selected', false);
                                            $toSelect.find('option[value="' + inmate.toInstitutionId + '"]').prop('selected', true);
                                            console.log('To dropdown value after option selection:', $toSelect.val());
                                        }

                                        // Trigger change event to ensure proper binding
                                        $toSelect.trigger('change');

                                        // Final verification
                                        console.log('Final To dropdown value:', $toSelect.val());
                                        console.log('Expected value:', inmate.toInstitutionId);
                                    }
                                }

                                $row.find('input[id*="SchDate"]').val(inmate.schDate);
                                $row.find('input[id*="Descrl"]').val(inmate.descrl);

                                console.log('Populated row ' + (i + 1) + ' with inmate:', inmate.inmateIdPrefix + inmate.offenderId);
                            });

                            updateButtonState();
                            alert(result.message);
                        } else {
                            alert('Error: ' + result.message);
                        }
                    },
                    error: function (xhr, status, error) {
                        $('#btnFindOffender').prop('disabled', false).html('Find Inmate');
                        console.error('AJAX Error:', status, error);
                        alert('Error finding inmate. See console for details.');
                    }
                });
            });

            // Enable Enter key to trigger server-side search
            $("#txtInmateNum").keyup(function (event) {
                if (event.keyCode == 13) {
                    $("#btnFindInmateServer").click();
                }
            });


        });
        </script>
    }
}

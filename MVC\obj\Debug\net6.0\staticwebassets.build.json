{"Version": 1, "Hash": "eaWZXxvNkSQPz/DRKupIf+ac9YcDwCd27KE3kahVkxA=", "Source": "MvcApp", "BasePath": "_content/MvcApp", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "MvcApp\\wwwroot", "Source": "MvcApp", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\MVC\\wwwroot\\", "BasePath": "_content/MvcApp", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\MVC\\wwwroot\\css\\site.css", "SourceId": "MvcApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\MVC\\wwwroot\\", "BasePath": "_content/MvcApp", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\MVC\\wwwroot\\js\\site.js", "SourceId": "MvcApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\MVC\\wwwroot\\", "BasePath": "_content/MvcApp", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\MVC\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "MvcApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\MVC\\wwwroot\\", "BasePath": "_content/MvcApp", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\MVC\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "MvcApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\MVC\\wwwroot\\", "BasePath": "_content/MvcApp", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\MVC\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "MvcApp", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\MV\\MV\\MVC\\wwwroot\\", "BasePath": "_content/MvcApp", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js"}]}